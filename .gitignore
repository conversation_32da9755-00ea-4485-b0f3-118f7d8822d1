# Environment variables
.env
env.txt
my_windows_env.txt
my_wsl_env.txt

# Docker
.dockerignore
**/docker-compose.yml
**/Dockerfile 

# Dependency directories
/node_modules
/venv

# Environment variables
.env
.env.*
env.txt

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
n8nEventLog*.log

# Backups and data
/n8n_backup_2/
/n8n_data/
/local-llama-wsl2-server_flowise_data/
*.sqlite
*.journal
*.tar.gz

# OS-generated files
.DS_Store
Thumbs.db

# IDEs and editors
.vscode/
.idea/
*.swp
*.swo