{"name": "Jerry<PERSON><PERSON><PERSON><PERSON><PERSON>", "nodes": [{"parameters": {"method": "POST", "url": "https://llm.jerryagenyi.xyz/v1/chat/completions", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "Authorization", "value": "Bearer sk_622f859d0f312e36c1598da4895eb18495392024969198ea44a2c6b3b900da62"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"model\": \"ollama/mistral-small3.2:latest\",\n  \"messages\": [\n    {\n      \"role\": \"system\",\n      \"content\": \"You are a helpful assistant.\"\n    },\n    {\n      \"role\": \"user\",\n      \"content\": \"{{ $json.chatInput }}\"\n    }\n  ]\n}\n", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-260, -120], "id": "3512dca0-f7cd-4051-899f-3281318406e5", "name": "HTTP Request"}, {"parameters": {"promptType": "define", "text": "={{ $('<PERSON><PERSON><PERSON> Trigger').item.json.message.text }}", "options": {"systemMessage": "You are a helpful AI assistant responding to users in a Telegram chat. Follow these guidelines:\n\n**Response Style:**\n- Be conversational and friendly\n- Keep responses concise but informative (aim for 1-3 paragraphs)\n- Use simple language that's easy to read on mobile devices\n- Avoid overly technical jargon unless specifically asked\n\n**Formatting:**\n- Use *bold* for emphasis when needed\n- Use `code` for technical terms or commands\n- Use bullet points (•) for lists when helpful\n- Keep line breaks reasonable for mobile reading\n\n**Behavior:**\n- Be direct and helpful\n- If you're unsure about something, acknowledge it honestly\n- For complex topics, offer to explain further if the user wants more detail\n- Stay focused on the user's question\n\n**Context:**\n- This is a casual chat environment\n- Users expect quick, practical answers\n- You can ask follow-up questions to clarify if needed\n\nRemember: Your thinking process will be filtered out automatically, so think through problems thoroughly but keep your final response clear and user-friendly."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [520, 20], "id": "a2effdeb-4806-40ab-94e4-dc5dcd54cae0", "name": "AI Agent"}, {"parameters": {"updates": ["message"], "additionalFields": {}}, "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.2, "position": [40, 20], "id": "c3e02616-3296-4079-a058-e4eaf1a2e326", "name": "<PERSON>eg<PERSON>", "webhookId": "d5b6e379-2181-4101-acc1-a80cd94ba2f5", "credentials": {"telegramApi": {"id": "TQKCpPlFRICDDjk3", "name": "Telegram account"}}}, {"parameters": {"operation": "editMessageText", "chatId": "={{ $('Telegram Trigger').item.json.message.chat.id }}", "messageId": "={{ $('Thinking...').item.json.result.message_id }}", "text": "={{ $json.text }}", "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1080, 20], "id": "ba0a5c3e-f0aa-46b0-868f-fb84818e6b14", "name": "Telegram", "webhookId": "384220e2-8549-43e4-b67b-7c9e6676d707", "credentials": {"telegramApi": {"id": "TQKCpPlFRICDDjk3", "name": "Telegram account"}}}, {"parameters": {"jsCode": "// Get the AI response\nconst aiResponse = $json.output || $json.response || $json.text || $json.content;\n\n// Function to remove <think></think> blocks and clean response\nfunction cleanDeepSeekResponse(text) {\n  if (!text) return \"Sorry, I couldn't generate a response.\";\n  \n  // Remove <think>...</think> blocks (including multiline)\n  let cleaned = text.replace(/<think>[\\s\\S]*?<\\/think>/gi, '');\n  \n  // Remove any remaining opening/closing think tags\n  cleaned = cleaned.replace(/<\\/?think>/gi, '');\n  \n  // Clean up extra whitespace and newlines\n  cleaned = cleaned.replace(/\\n\\s*\\n\\s*\\n/g, '\\n\\n'); // Multiple newlines to double\n  cleaned = cleaned.trim();\n  \n  // Fallback if response is empty after cleaning\n  if (!cleaned || cleaned.length < 3) {\n    return \"I processed your request but couldn't provide a clear response. Please try rephrasing your question.\";\n  }\n  \n  return cleaned;\n}\n\n// Clean the response\nconst cleanedResponse = cleanDeepSeekResponse(aiResponse);\n\n// Return for Telegram node\nreturn {\n  json: {\n    text: cleanedResponse,\n    chat_id: $node['Telegram Trigger'].json.message.chat.id,\n    // Preserve other useful data\n    original_message: $node['Telegram Trigger'].json.message.text,\n    user_id: $node['Telegram Trigger'].json.message.from.id,\n    username: $node['Telegram Trigger'].json.message.from.username || 'Unknown'\n  }\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [880, 20], "id": "99e97cd2-241d-469a-91c8-788c8a63af68", "name": "Code Node: Cleanup LLM response"}, {"parameters": {"chatId": "={{ $json.message.chat.id }}", "text": "🤔 Thinking...", "additionalFields": {"appendAttribution": false}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [260, 20], "id": "c6437227-f162-4c26-9b08-88a4d5fa34ae", "name": "Thinking...", "webhookId": "abdb848e-84a6-4ab5-9cc8-ebc2c1d1f78e", "credentials": {"telegramApi": {"id": "TQKCpPlFRICDDjk3", "name": "Telegram account"}}}, {"parameters": {"contextWindowLength": 20}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [620, 240], "id": "d89c65b0-9b8f-4600-b393-0b969dd55734", "name": "Simple Memory"}, {"parameters": {"options": {}}, "type": "n8n-nodes-bravesearch-advanced.toolBraveSearch", "typeVersion": 1, "position": [780, 240], "id": "63c64cf0-6c8f-494b-898b-bfd03784a0ed", "name": "Web Search (Brave)", "credentials": {"braveSearchApi": {"id": "d7cq0j5CSagCv2eY", "name": "Brave Search account"}}}, {"parameters": {"model": "MFDoom/deepseek-r1-tool-calling:14b", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOllama", "typeVersion": 1, "position": [260, 320], "id": "08f0aa23-7906-46a6-85d1-6f9e9eddb4df", "name": "<PERSON><PERSON><PERSON> Chat Model", "credentials": {"ollamaApi": {"id": "LCZoQk2NhwF0pjrC", "name": "Ollama account"}}}, {"parameters": {"model": {"__rl": true, "value": "gpt-4o-mini-search-preview-2025-03-11", "mode": "list", "cachedResultName": "gpt-4o-mini-search-preview-2025-03-11"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [440, 400], "id": "92b0ff5a-f5aa-465d-ae5e-c7aa0807c28d", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "DgzSkcDaInP7I7st", "name": "OpenAi account"}}}], "pinData": {}, "connections": {"Telegram Trigger": {"main": [[{"node": "Thinking...", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Code Node: Cleanup LLM response", "type": "main", "index": 0}]]}, "Code Node: Cleanup LLM response": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}, "Thinking...": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Web Search (Brave)": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Ollama Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "d16eb10a-d15d-4b0b-818e-a37b7fd58760", "meta": {"templateCredsSetupCompleted": true, "instanceId": "36fb506f456c7662175a45c19abbbc4dc54e457a46b5105ba965d83486ff7566"}, "id": "L26RSQ4GCCiJq4oj", "tags": []}