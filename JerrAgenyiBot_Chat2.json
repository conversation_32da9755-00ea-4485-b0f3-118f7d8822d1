{"name": "DeepSeekBot (with <PERSON>)", "nodes": [{"id": "TelegramTrigger", "name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1, "position": [100, 300], "parameters": {"updates": ["message"]}, "credentials": {"telegramApi": {"id": "YOUR_ID", "name": "Telegram Bot"}}}, {"id": "Memory", "name": "Simple Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1, "position": [300, 100], "parameters": {"contextWindowLength": 20}}, {"id": "Thinking", "name": "Thinking...", "type": "n8n-nodes-base.telegram", "typeVersion": 1, "position": [300, 300], "parameters": {"chatId": "={{ $json.message.chat.id }}", "text": "🤔 Thinking...", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "YOUR_ID", "name": "Telegram Bot"}}}, {"id": "LLM", "name": "<PERSON><PERSON><PERSON> Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOllama", "typeVersion": 1, "position": [520, 300], "parameters": {"model": "YOUR_OLLAMA_MODEL_NAME"}, "credentials": {"ollamaApi": {"id": "YOUR_ID", "name": "Ollama"}}}, {"id": "Brave", "name": "Brave Search HTTP", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [520, 500], "parameters": {"url": "https://api.search.brave.com/res/v1/search", "method": "GET", "queryParameters": [{"name": "q", "value": "={{ $json.parameters.q }}"}, {"name": "count", "value": "5"}], "headerParameters": [{"name": "Authorization", "value": "Bearer YOUR_API_KEY"}], "options": {}}}, {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "Extract Tool Call", "type": "n8n-nodes-base.code", "typeVersion": 1, "position": [720, 300], "parameters": {"jsCode": "// If tool call present\nconst call = $json.tool_calls?.[0];\nif (call && call.function?.name === \"web_search\") {\n  return [{ json: { tool: 'web_search', parameters: call.function.parameters } }];\n} else {\n  return [{ json: { final: true, text: $json.output } }];\n}"}}, {"id": "ReturnToModel", "name": "Rerun with <PERSON><PERSON>t", "type": "@n8n/n8n-nodes-langchain.lmChatOllama", "typeVersion": 1, "position": [920, 300], "parameters": {"model": "YOUR_OLLAMA_MODEL_NAME"}, "credentials": {"ollamaApi": {"id": "YOUR_ID", "name": "Ollama"}}}, {"id": "Reply", "name": "Reply to Telegram", "type": "n8n-nodes-base.telegram", "typeVersion": 1, "position": [1100, 300], "parameters": {"chatId": "={{ $json.chat_id || $json.message.chat.id }}", "text": "={{ $json.text || $json.output }}"}, "credentials": {"telegramApi": {"id": "YOUR_ID", "name": "Telegram Bot"}}}], "connections": {"Telegram Trigger": {"main": [[{"node": "Thinking", "type": "main", "index": 0}], [{"node": "Memory", "type": "main", "index": 0}]]}, "Thinking": {"main": [[{"node": "LLM", "type": "main", "index": 0}]]}, "Memory": {"ai_memory": [[{"node": "LLM", "type": "ai_memory", "index": 0}]]}, "LLM": {"main": [[{"node": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "ToolCallParser": {"main": [[{"node": "Reply", "type": "main", "index": 0}], [{"node": "Brave", "type": "main", "index": 0}]]}, "Brave": {"main": [[{"node": "ReturnToModel", "type": "main", "index": 0}]]}, "ReturnToModel": {"main": [[{"node": "Reply", "type": "main", "index": 0}]]}}}